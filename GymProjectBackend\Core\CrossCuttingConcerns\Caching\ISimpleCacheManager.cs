namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Basit cache operasyonları için interface
    /// AdvancedRateLimitManager gibi sistemler için minimal cache desteği
    /// </summary>
    public interface ISimpleCacheManager
    {
        /// <summary>
        /// Cache'de key var mı kontrol et
        /// </summary>
        bool IsAdd(string key);
        
        /// <summary>
        /// Cache'den değer al
        /// </summary>
        T Get<T>(string key);
        
        /// <summary>
        /// Cache'e değer ekle
        /// </summary>
        void Add(string key, object value, int durationMinutes);
        
        /// <summary>
        /// Cache'den değer sil
        /// </summary>
        void Remove(string key);
    }
}
