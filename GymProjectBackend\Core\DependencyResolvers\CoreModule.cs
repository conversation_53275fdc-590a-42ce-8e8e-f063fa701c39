﻿using Core.CrossCuttingConcerns.Caching;
using Core.CrossCuttingConcerns.Logging.FileLogger;
using Core.CrossCuttingConcerns.Logging;
using Core.Utilities.IoC;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics;

namespace Core.DependencyResolvers
{
    public class CoreModule : ICoreModule
    {
        public void Load(IServiceCollection serviceCollection)
        {
            // Memory Cache
            serviceCollection.AddMemoryCache();

            // HTTP Context
            serviceCollection.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            // Company Context (Multi-tenant)
            serviceCollection.AddScoped<Core.Utilities.Security.CompanyContext.ICompanyContext, Core.Utilities.Security.CompanyContext.CompanyContext>();

            // Simple Cache Manager (for AdvancedRateLimitManager)
            serviceCollection.AddSingleton<ISimpleCacheManager, SimpleCacheManager>();

            // Logging
            serviceCollection.AddSingleton<Stopwatch>();
            serviceCollection.AddSingleton<FileLoggerService>();
            serviceCollection.AddSingleton<PerformanceLoggerService>();
            serviceCollection.AddSingleton<ILogService, FileLoggerService>();
        }
    }
}
