using Core.CrossCuttingConcerns.Caching.Configuration;
using Core.CrossCuttingConcerns.Caching.KeyGeneration;
using Core.CrossCuttingConcerns.Caching.Models;
using Core.CrossCuttingConcerns.Caching.Redis;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching.Hybrid
{
    /// <summary>
    /// Hybrid cache manager - L1 (Memory) + L2 (Redis) cache layers
    /// Thread-safe ve high-performance multi-tenant cache sistemi
    /// </summary>
    public class HybridCacheManager : ICacheManager
    {
        private readonly IMemoryCache _memoryCache;
        private readonly RedisCacheManager _redisCache;
        private readonly ICacheKeyGenerator _keyGenerator;
        private readonly CacheConfiguration _configuration;
        private readonly RedisConfiguration _redisConfiguration;
        private readonly ILogger<HybridCacheManager> _logger;
        private readonly CacheStatistics _statistics;
        
        // Thread-safe collections
        private readonly ConcurrentDictionary<string, CacheItem> _cacheItems;
        private readonly ConcurrentDictionary<string, HashSet<string>> _tagIndex;
        private readonly object _lockObject = new object();

        public HybridCacheManager(
            IMemoryCache memoryCache,
            RedisCacheManager redisCache,
            ICacheKeyGenerator keyGenerator,
            CacheConfiguration configuration,
            RedisConfiguration redisConfiguration,
            ILogger<HybridCacheManager> logger)
        {
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
            _redisCache = redisCache ?? throw new ArgumentNullException(nameof(redisCache));
            _keyGenerator = keyGenerator ?? throw new ArgumentNullException(nameof(keyGenerator));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _redisConfiguration = redisConfiguration ?? throw new ArgumentNullException(nameof(redisConfiguration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            _statistics = new CacheStatistics();
            _cacheItems = new ConcurrentDictionary<string, CacheItem>();
            _tagIndex = new ConcurrentDictionary<string, HashSet<string>>();
            
            // Redis invalidation subscription
            InitializeInvalidationSubscription();
        }

        public T Get<T>(string key)
        {
            try
            {
                var tenantId = _keyGenerator.ExtractTenantId(key);
                var entityName = _keyGenerator.ExtractEntityName(key);

                // L1 Cache (Memory) - En hızlı
                if (_redisConfiguration.EnableL1Cache && _memoryCache.TryGetValue(key, out T memoryValue))
                {
                    _statistics.RecordHit(tenantId, entityName);
                    LogDebug($"L1 HIT: {key}");
                    return memoryValue;
                }

                // L2 Cache (Redis) - Orta hızlı
                if (_redisConfiguration.EnableL2Cache)
                {
                    var redisValue = _redisCache.Get<T>(key);
                    if (redisValue != null)
                    {
                        // Redis'ten bulduğumuz veriyi Memory'ye de koy (cache warming)
                        if (_redisConfiguration.EnableL1Cache)
                        {
                            var memoryOptions = new MemoryCacheEntryOptions
                            {
                                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5), // L1 için kısa süre
                                Priority = CacheItemPriority.High,
                                Size = 1
                            };
                            _memoryCache.Set(key, redisValue, memoryOptions);
                        }

                        _statistics.RecordHit(tenantId, entityName);
                        LogDebug($"L2 HIT: {key} (promoted to L1)");
                        return redisValue;
                    }
                }

                _statistics.RecordMiss(tenantId, entityName);
                LogDebug($"CACHE MISS: {key}");
                return default(T);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hybrid cache GET error for key: {Key}", key);
                return default(T);
            }
        }

        public object Get(string key)
        {
            return Get<object>(key);
        }

        public void Add(string key, object value, int durationMinutes)
        {
            Add(key, value, durationMinutes, null);
        }

        public void Add(string key, object value, int durationMinutes, string[] tags)
        {
            try
            {
                var tenantId = _keyGenerator.ExtractTenantId(key);
                var entityName = _keyGenerator.ExtractEntityName(key);
                var expiration = TimeSpan.FromMinutes(durationMinutes);

                // L1 Cache (Memory) - Sık kullanılan data için
                if (_redisConfiguration.EnableL1Cache)
                {
                    var memoryOptions = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(Math.Min(durationMinutes, 10)), // Max 10 dakika
                        Priority = CacheItemPriority.Normal,
                        Size = 1
                    };
                    _memoryCache.Set(key, value, memoryOptions);
                }

                // L2 Cache (Redis) - Uzun süreli data için
                if (_redisConfiguration.EnableL2Cache)
                {
                    _redisCache.Set(key, value, expiration);
                }

                // Cache item metadata'sını sakla
                var cacheItem = new CacheItem
                {
                    Key = key,
                    TenantId = tenantId,
                    EntityName = entityName,
                    Tags = tags ?? Array.Empty<string>(),
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(durationMinutes)
                };

                _cacheItems.TryAdd(key, cacheItem);
                UpdateTagIndex(key, cacheItem.Tags);

                _statistics.RecordSet(tenantId, entityName);
                LogDebug($"HYBRID SET: {key}, Duration: {durationMinutes}min, Tags: [{string.Join(", ", tags ?? Array.Empty<string>())}]");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hybrid cache ADD error for key: {Key}", key);
            }
        }

        public bool IsAdd(string key)
        {
            try
            {
                // Önce L1'de kontrol et
                if (_redisConfiguration.EnableL1Cache && _memoryCache.TryGetValue(key, out _))
                {
                    return true;
                }

                // Sonra L2'de kontrol et
                if (_redisConfiguration.EnableL2Cache)
                {
                    return _redisCache.Exists(key);
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hybrid cache EXISTS error for key: {Key}", key);
                return false;
            }
        }

        public void Remove(string key)
        {
            try
            {
                // L1'den sil
                if (_redisConfiguration.EnableL1Cache)
                {
                    _memoryCache.Remove(key);
                }

                // L2'den sil
                if (_redisConfiguration.EnableL2Cache)
                {
                    _redisCache.Remove(key);
                }

                // Metadata'yı temizle
                if (_cacheItems.TryRemove(key, out var cacheItem))
                {
                    RemoveFromTagIndex(key, cacheItem.Tags);
                    _statistics.RecordRemoval(cacheItem.TenantId, cacheItem.EntityName);
                }

                // Diğer server'lara invalidation mesajı gönder
                PublishInvalidation("cache:remove", key);

                LogDebug($"HYBRID REMOVE: {key}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hybrid cache REMOVE error for key: {Key}", key);
            }
        }

        public void RemoveByPattern(string pattern)
        {
            try
            {
                var keysToRemove = GetKeysByPattern(pattern);
                LogDebug($"HYBRID REMOVE BY PATTERN: {pattern}, Found {keysToRemove.Length} keys");

                foreach (var key in keysToRemove)
                {
                    Remove(key);
                }

                // Redis'ten de pattern ile sil
                if (_redisConfiguration.EnableL2Cache)
                {
                    _redisCache.RemoveByPattern(pattern);
                }

                // Diğer server'lara invalidation mesajı gönder
                PublishInvalidation("cache:removePattern", pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hybrid cache REMOVE BY PATTERN error for pattern: {Pattern}", pattern);
            }
        }

        public void RemoveByTags(string[] tags)
        {
            try
            {
                var keysToRemove = new HashSet<string>();

                lock (_lockObject)
                {
                    foreach (var tag in tags)
                    {
                        if (_tagIndex.TryGetValue(tag, out var taggedKeys))
                        {
                            foreach (var key in taggedKeys.ToArray()) // ToArray() ile thread-safe copy
                            {
                                keysToRemove.Add(key);
                            }
                        }
                    }
                }

                LogDebug($"HYBRID REMOVE BY TAGS: [{string.Join(", ", tags)}], Found {keysToRemove.Count} keys");

                foreach (var key in keysToRemove)
                {
                    Remove(key);
                }

                // Diğer server'lara invalidation mesajı gönder
                PublishInvalidation("cache:removeTags", string.Join(",", tags));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hybrid cache REMOVE BY TAGS error for tags: [{Tags}]", string.Join(", ", tags));
            }
        }

        public void RemoveByTenant(int tenantId)
        {
            var pattern = _keyGenerator.GeneratePattern(tenantId);
            RemoveByPattern(pattern);
        }

        public void RemoveByEntity(int tenantId, string entityName)
        {
            var pattern = _keyGenerator.GeneratePattern(tenantId, entityName);
            RemoveByPattern(pattern);
        }

        public void RemoveByEntityAndOperation(int tenantId, string entityName, string operation)
        {
            var pattern = _keyGenerator.GeneratePattern(tenantId, entityName, operation);
            RemoveByPattern(pattern);
        }

        public Dictionary<string, T> GetMultiple<T>(string[] keys)
        {
            var result = new Dictionary<string, T>();
            foreach (var key in keys)
            {
                var value = Get<T>(key);
                if (value != null)
                {
                    result[key] = value;
                }
            }
            return result;
        }

        public void AddMultiple(Dictionary<string, object> items, int durationMinutes)
        {
            foreach (var item in items)
            {
                Add(item.Key, item.Value, durationMinutes);
            }
        }

        public void RemoveMultiple(string[] keys)
        {
            foreach (var key in keys)
            {
                Remove(key);
            }
        }

        public void Clear()
        {
            try
            {
                // L1 cache temizle (Memory cache'in Clear metodu yok, yeni instance oluşturmak gerekir)
                // Bu durumda pattern ile tüm key'leri sil
                var allKeys = _cacheItems.Keys.ToArray();
                foreach (var key in allKeys)
                {
                    _memoryCache.Remove(key);
                }

                // L2 cache temizle (Redis)
                if (_redisConfiguration.EnableL2Cache)
                {
                    var pattern = "*";
                    _redisCache.RemoveByPattern(pattern);
                }

                // Metadata temizle
                _cacheItems.Clear();
                _tagIndex.Clear();
                _statistics.Reset();

                LogDebug("HYBRID CLEAR: All cache cleared");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hybrid cache CLEAR error");
            }
        }

        public void ClearTenant(int tenantId)
        {
            RemoveByTenant(tenantId);
        }

        public long GetCacheSize()
        {
            return _cacheItems.Count;
        }

        public string[] GetAllKeys()
        {
            return _cacheItems.Keys.ToArray();
        }

        public string[] GetKeysByPattern(string pattern)
        {
            try
            {
                // Basit pattern matching (Redis SCAN benzeri)
                var regex = new System.Text.RegularExpressions.Regex(
                    "^" + System.Text.RegularExpressions.Regex.Escape(pattern).Replace("\\*", ".*") + "$",
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                return _cacheItems.Keys.Where(key => regex.IsMatch(key)).ToArray();
            }
            catch
            {
                // Regex hatası durumunda basit string matching
                return _cacheItems.Keys.Where(key => key.Contains(pattern.Replace("*", ""))).ToArray();
            }
        }

        public CacheStatistics GetStatistics()
        {
            return _statistics;
        }

        public void ResetStatistics()
        {
            _statistics.Reset();
        }

        public bool IsHealthy()
        {
            try
            {
                var testKey = "health_check_" + Guid.NewGuid();
                var testValue = "test";

                Add(testKey, testValue, 1);
                var retrieved = Get<string>(testKey);
                Remove(testKey);

                var redisHealthy = !_redisConfiguration.EnableL2Cache || _redisCache.IsHealthy();

                return retrieved == testValue && redisHealthy;
            }
            catch
            {
                return false;
            }
        }

        public Dictionary<string, object> GetHealthInfo()
        {
            var info = new Dictionary<string, object>
            {
                ["IsHealthy"] = IsHealthy(),
                ["CacheSize"] = GetCacheSize(),
                ["Statistics"] = GetStatistics(),
                ["Configuration"] = _configuration,
                ["RedisConfiguration"] = _redisConfiguration,
                ["MemoryPressure"] = GC.GetTotalMemory(false)
            };

            if (_redisConfiguration.EnableL2Cache)
            {
                info["RedisHealth"] = _redisCache.GetHealthInfo();
            }

            return info;
        }

        public List<int> GetActiveTenants()
        {
            return _cacheItems.Values
                .Select(item => item.TenantId)
                .Distinct()
                .Where(id => id > 0)
                .ToList();
        }

        public Dictionary<string, int> GetEntityCounts(int tenantId)
        {
            return _cacheItems.Values
                .Where(item => item.TenantId == tenantId)
                .GroupBy(item => item.EntityName)
                .ToDictionary(g => g.Key, g => g.Count());
        }

        public List<CacheItemDetail> GetCacheDetails(int tenantId)
        {
            return _cacheItems.Values
                .Where(item => item.TenantId == tenantId)
                .Select(item => new CacheItemDetail
                {
                    Key = item.Key,
                    TenantId = item.TenantId,
                    EntityName = item.EntityName,
                    Tags = item.Tags,
                    CreatedAt = item.CreatedAt,
                    ExpiresAt = item.ExpiresAt,
                    SizeInBytes = EstimateSize(item),
                    ValueType = "Object"
                })
                .ToList();
        }

        public CacheItemDetail GetCacheItemDetail(string key)
        {
            if (_cacheItems.TryGetValue(key, out var item))
            {
                return new CacheItemDetail
                {
                    Key = item.Key,
                    TenantId = item.TenantId,
                    EntityName = item.EntityName,
                    Tags = item.Tags,
                    CreatedAt = item.CreatedAt,
                    ExpiresAt = item.ExpiresAt,
                    SizeInBytes = EstimateSize(item),
                    ValueType = "Object"
                };
            }
            return null;
        }

        public long GetTenantCacheSize(int tenantId)
        {
            return _cacheItems.Values.Count(item => item.TenantId == tenantId);
        }

        public Dictionary<int, long> GetAllTenantSizes()
        {
            return _cacheItems.Values
                .GroupBy(item => item.TenantId)
                .ToDictionary(g => g.Key, g => (long)g.Count());
        }

        public Dictionary<string, object> GetPerformanceMetrics()
        {
            var stats = GetStatistics();
            return new Dictionary<string, object>
            {
                ["TotalHits"] = stats.TotalHits,
                ["TotalMisses"] = stats.TotalMisses,
                ["HitRatio"] = stats.HitRatio,
                ["TotalSets"] = stats.TotalSets,
                ["TotalRemovals"] = stats.TotalRemovals,
                ["CacheSize"] = GetCacheSize(),
                ["ActiveTenants"] = GetActiveTenants().Count,
                ["MemoryUsage"] = GC.GetTotalMemory(false)
            };
        }

        private void UpdateTagIndex(string key, string[] tags)
        {
            foreach (var tag in tags)
            {
                _tagIndex.AddOrUpdate(tag,
                    new HashSet<string> { key },
                    (_, existingSet) =>
                    {
                        lock (existingSet)
                        {
                            existingSet.Add(key);
                            return existingSet;
                        }
                    });
            }
        }

        private void RemoveFromTagIndex(string key, string[] tags)
        {
            foreach (var tag in tags)
            {
                if (_tagIndex.TryGetValue(tag, out var taggedKeys))
                {
                    lock (taggedKeys)
                    {
                        taggedKeys.Remove(key);
                        if (taggedKeys.Count == 0)
                        {
                            _tagIndex.TryRemove(tag, out _);
                        }
                    }
                }
            }
        }

        private void InitializeInvalidationSubscription()
        {
            if (_redisConfiguration.EnableL2Cache)
            {
                Task.Run(async () =>
                {
                    await _redisCache.SubscribeToInvalidationAsync("cache:remove", (channel, message) =>
                    {
                        if (_redisConfiguration.EnableL1Cache)
                        {
                            _memoryCache.Remove(message);
                        }
                    });

                    await _redisCache.SubscribeToInvalidationAsync("cache:removePattern", (channel, pattern) =>
                    {
                        if (_redisConfiguration.EnableL1Cache)
                        {
                            var keysToRemove = GetKeysByPattern(pattern);
                            foreach (var key in keysToRemove)
                            {
                                _memoryCache.Remove(key);
                            }
                        }
                    });

                    await _redisCache.SubscribeToInvalidationAsync("cache:removeTags", (channel, tagsStr) =>
                    {
                        if (_redisConfiguration.EnableL1Cache)
                        {
                            var tags = tagsStr.Split(',');
                            RemoveByTags(tags);
                        }
                    });
                });
            }
        }

        private void PublishInvalidation(string channel, string message)
        {
            if (_redisConfiguration.EnableL2Cache)
            {
                _redisCache.PublishInvalidation(channel, message);
            }
        }

        private long EstimateSize(CacheItem item)
        {
            // Basit size estimation
            return item.Key.Length * 2 + item.EntityName.Length * 2 + 100;
        }

        private void LogDebug(string message)
        {
            if (_configuration.EnableDebugLogging)
            {
                _logger.LogDebug(message);
            }
        }
    }
}
