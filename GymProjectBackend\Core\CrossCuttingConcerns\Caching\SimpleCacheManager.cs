using Microsoft.Extensions.Caching.Memory;
using System;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// IMemoryCache kullanarak basit cache operasyonları
    /// AdvancedRateLimitManager gibi sistemler için minimal cache desteği
    /// </summary>
    public class SimpleCacheManager : ISimpleCacheManager
    {
        private readonly IMemoryCache _memoryCache;

        public SimpleCacheManager(IMemoryCache memoryCache)
        {
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
        }

        public bool IsAdd(string key)
        {
            if (string.IsNullOrEmpty(key))
                return false;

            return _memoryCache.TryGetValue(key, out _);
        }

        public T Get<T>(string key)
        {
            if (string.IsNullOrEmpty(key))
                return default(T);

            if (_memoryCache.TryGetValue(key, out T value))
            {
                return value;
            }

            return default(T);
        }

        public void Add(string key, object value, int durationMinutes)
        {
            if (string.IsNullOrEmpty(key) || value == null)
                return;

            var options = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(durationMinutes),
                Priority = CacheItemPriority.Normal
            };

            _memoryCache.Set(key, value, options);
        }

        public void Remove(string key)
        {
            if (string.IsNullOrEmpty(key))
                return;

            _memoryCache.Remove(key);
        }
    }
}
